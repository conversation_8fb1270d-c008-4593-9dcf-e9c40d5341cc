require 'rails_helper'

RSpec.describe 'Resolution Motives API', type: :request do
  let!(:account) { create(:account) }
  let!(:resolution_motive) { create(:resolution_motive, account: account) }

  describe 'GET /api/v1/accounts/{account.id}/resolution_motives' do
    context 'when it is an unauthenticated user' do
      it 'returns unauthorized' do
        get "/api/v1/accounts/#{account.id}/resolution_motives"

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when it is an authenticated user' do
      let(:agent) { create(:user, account: account, role: :administrator) }

      it 'returns all the resolution motives in account' do
        get "/api/v1/accounts/#{account.id}/resolution_motives",
            headers: agent.create_new_auth_token,
            as: :json

        expect(response).to have_http_status(:success)
        expect(response.body).to include(resolution_motive.name)
      end
    end
  end

  describe 'GET /api/v1/accounts/{account.id}/resolution_motives/:id' do
    context 'when it is an unauthenticated user' do
      it 'returns unauthorized' do
        get "/api/v1/accounts/#{account.id}/resolution_motives/#{resolution_motive.id}"

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when it is an authenticated user' do
      let(:admin) { create(:user, account: account, role: :administrator) }

      it 'shows the resolution motive' do
        get "/api/v1/accounts/#{account.id}/resolution_motives/#{resolution_motive.id}",
            headers: admin.create_new_auth_token,
            as: :json

        expect(response).to have_http_status(:success)
        expect(response.body).to include(resolution_motive.name)
      end
    end
  end

  describe 'POST /api/v1/accounts/{account.id}/resolution_motives' do
    let(:admin) { create(:user, account: account, role: :administrator) }

    context 'when it is an unauthenticated user' do
      it 'returns unauthorized' do
        post "/api/v1/accounts/#{account.id}/resolution_motives"

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when it is an authenticated user' do
      let(:valid_params) do
        {
          resolution_motive: {
            name: 'Test Motive',
            description: 'Test Description',
            active: true
          }
        }
      end

      it 'creates a new resolution motive' do
        post "/api/v1/accounts/#{account.id}/resolution_motives",
             headers: admin.create_new_auth_token,
             params: valid_params,
             as: :json

        expect(response).to have_http_status(:success)
        expect(account.resolution_motives.count).to eq(2)
        expect(account.resolution_motives.last.name).to eq('Test Motive')
      end

      it 'returns error for invalid params' do
        post "/api/v1/accounts/#{account.id}/resolution_motives",
             headers: admin.create_new_auth_token,
             params: { resolution_motive: { name: '' } },
             as: :json

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'PATCH /api/v1/accounts/{account.id}/resolution_motives/:id' do
    let(:admin) { create(:user, account: account, role: :administrator) }

    context 'when it is an unauthenticated user' do
      it 'returns unauthorized' do
        patch "/api/v1/accounts/#{account.id}/resolution_motives/#{resolution_motive.id}"

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when it is an authenticated user' do
      let(:valid_params) do
        {
          resolution_motive: {
            name: 'Updated Motive',
            description: 'Updated Description'
          }
        }
      end

      it 'updates the resolution motive' do
        patch "/api/v1/accounts/#{account.id}/resolution_motives/#{resolution_motive.id}",
              headers: admin.create_new_auth_token,
              params: valid_params,
              as: :json

        expect(response).to have_http_status(:success)
        expect(resolution_motive.reload.name).to eq('Updated Motive')
      end
    end
  end

  describe 'DELETE /api/v1/accounts/{account.id}/resolution_motives/:id' do
    let(:admin) { create(:user, account: account, role: :administrator) }

    context 'when it is an unauthenticated user' do
      it 'returns unauthorized' do
        delete "/api/v1/accounts/#{account.id}/resolution_motives/#{resolution_motive.id}"

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when it is an authenticated user' do
      it 'deletes the resolution motive when not in use' do
        delete "/api/v1/accounts/#{account.id}/resolution_motives/#{resolution_motive.id}",
               headers: admin.create_new_auth_token,
               as: :json

        expect(response).to have_http_status(:ok)
        expect(account.resolution_motives.count).to eq(0)
      end

      it 'returns error when resolution motive is in use' do
        conversation = create(:conversation, account: account, resolution_motive: resolution_motive)
        
        delete "/api/v1/accounts/#{account.id}/resolution_motives/#{resolution_motive.id}",
               headers: admin.create_new_auth_token,
               as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        expect(account.resolution_motives.count).to eq(1)
      end
    end
  end
end
