require 'rails_helper'

RSpec.describe ResolutionMotive do
  describe 'associations' do
    it { is_expected.to belong_to(:account) }
    it { is_expected.to have_many(:conversations).dependent(:nullify) }
  end

  describe 'validations' do
    let(:account) { create(:account) }
    
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_length_of(:name).is_at_most(100) }
    it { is_expected.to validate_length_of(:description).is_at_most(500) }
    it { is_expected.to validate_presence_of(:position) }
    it { is_expected.to validate_numericality_of(:position).is_greater_than_or_equal_to(0) }

    it 'validates uniqueness of name within account' do
      create(:resolution_motive, account: account, name: 'Test Motive')
      duplicate_motive = build(:resolution_motive, account: account, name: 'Test Motive')
      expect(duplicate_motive).not_to be_valid
      expect(duplicate_motive.errors[:name]).to include('has already been taken')
    end

    it 'allows same name in different accounts' do
      other_account = create(:account)
      create(:resolution_motive, account: account, name: 'Test Motive')
      duplicate_motive = build(:resolution_motive, account: other_account, name: 'Test Motive')
      expect(duplicate_motive).to be_valid
    end

    it 'validates case insensitive uniqueness' do
      create(:resolution_motive, account: account, name: 'Test Motive')
      duplicate_motive = build(:resolution_motive, account: account, name: 'test motive')
      expect(duplicate_motive).not_to be_valid
    end
  end

  describe 'scopes' do
    let(:account) { create(:account) }
    let!(:active_motive) { create(:resolution_motive, account: account, active: true, position: 2) }
    let!(:inactive_motive) { create(:resolution_motive, account: account, active: false, position: 1) }
    let!(:another_active_motive) { create(:resolution_motive, account: account, active: true, position: 1) }

    describe '.active' do
      it 'returns only active motives' do
        expect(ResolutionMotive.active).to include(active_motive, another_active_motive)
        expect(ResolutionMotive.active).not_to include(inactive_motive)
      end
    end

    describe '.ordered' do
      it 'returns motives ordered by position then name' do
        ordered_motives = ResolutionMotive.ordered
        expect(ordered_motives.first).to eq(another_active_motive) # position 1
        expect(ordered_motives.second).to eq(inactive_motive) # position 1 but alphabetically after
        expect(ordered_motives.third).to eq(active_motive) # position 2
      end
    end

    describe '.for_account' do
      let(:other_account) { create(:account) }
      let!(:other_account_motive) { create(:resolution_motive, account: other_account) }

      it 'returns motives for specific account only' do
        account_motives = ResolutionMotive.for_account(account)
        expect(account_motives).to include(active_motive, inactive_motive, another_active_motive)
        expect(account_motives).not_to include(other_account_motive)
      end
    end
  end

  describe 'callbacks' do
    describe 'before_validation :set_default_position' do
      let(:account) { create(:account) }

      it 'sets position to 1 for first motive' do
        motive = build(:resolution_motive, account: account, position: nil)
        motive.valid?
        expect(motive.position).to eq(1)
      end

      it 'sets position to max + 1 for subsequent motives' do
        create(:resolution_motive, account: account, position: 5)
        create(:resolution_motive, account: account, position: 3)
        
        motive = build(:resolution_motive, account: account, position: nil)
        motive.valid?
        expect(motive.position).to eq(6)
      end

      it 'does not change position if already set' do
        motive = build(:resolution_motive, account: account, position: 10)
        motive.valid?
        expect(motive.position).to eq(10)
      end
    end
  end

  describe 'instance methods' do
    let(:account) { create(:account) }
    let(:motive) { create(:resolution_motive, account: account, name: 'Test Motive') }

    describe '#display_name' do
      it 'returns the name' do
        expect(motive.display_name).to eq('Test Motive')
      end
    end

    describe '#can_be_deleted?' do
      it 'returns true when no conversations use this motive' do
        expect(motive.can_be_deleted?).to be true
      end

      it 'returns false when conversations use this motive' do
        conversation = create(:conversation, account: account, resolution_motive: motive)
        expect(motive.can_be_deleted?).to be false
      end
    end
  end
end
